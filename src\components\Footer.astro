---
const currentYear = new Date().getFullYear();
---

<!-- Modern 2025 Footer with Enhanced Glassmorphism -->
<footer role="contentinfo" class="relative overflow-hidden">
  <!-- Advanced Glassmorphism Background with Progressive Blur -->
  <div class="absolute inset-0 bg-gradient-to-br from-background-light/95 via-background-light/90 to-background-light-secondary/95 dark:from-background-dark/95 dark:via-background-dark/90 dark:to-background-dark-secondary/95 backdrop-blur-2xl"></div>
  
  <!-- Decorative Elements for Depth -->
  <div class="absolute inset-0 opacity-40">
    <div class="absolute top-0 left-1/4 w-96 h-96 bg-gradient-to-r from-primary-500/10 to-accent-500/10 rounded-full blur-3xl"></div>
    <div class="absolute bottom-0 right-1/3 w-80 h-80 bg-gradient-to-l from-accent-500/8 to-primary-500/8 rounded-full blur-3xl"></div>
  </div>
  
  <!-- Top Border with Sophisticated Glass Effect -->
  <div class="absolute top-0 inset-x-0 h-px bg-gradient-to-r from-transparent via-primary-500/30 to-transparent"></div>
  
  <!-- Main Footer Content -->
  <div class="relative z-10">
    <div class="container-custom py-16">
      <!-- Enhanced Main Content Grid -->
      <div class="grid grid-cols-1 lg:grid-cols-12 gap-12 mb-12">
        
        <!-- Brand & Vision Section (Enhanced) -->
        <div class="lg:col-span-7 space-y-8">
          <!-- Brand Header with Gradient -->
          <div class="brand-section space-y-6">
            <div class="space-y-3">
              <h3 class="text-3xl lg:text-4xl font-bold font-heading bg-gradient-to-r from-primary-700 via-primary-600 to-accent-600 bg-clip-text text-transparent">
                Nob Hokleng
              </h3>
              <div class="w-16 h-1 bg-gradient-to-r from-primary-500 to-accent-500 rounded-full"></div>
            </div>
            
            <p class="text-lg text-secondary-600 dark:text-secondary-300 leading-relaxed max-w-2xl">
              Software developer passionate about building <span class="font-semibold text-primary-600 dark:text-primary-400">scalable systems</span> and <span class="font-semibold text-primary-600 dark:text-primary-400">modern web applications</span>. Focused on clean code, system architecture, and continuous learning.
            </p>
          </div>
          
          <!-- Enhanced Social Links with Modern Interactions -->
          <div class="social-section">
            <h4 class="text-sm font-bold text-secondary-700 dark:text-secondary-300 mb-6 uppercase tracking-widest flex items-center gap-2">
              <span class="w-2 h-2 bg-gradient-to-r from-primary-500 to-accent-500 rounded-full"></span>
              Let's Connect
            </h4>
            <div class="flex flex-wrap gap-4">
              <a href="https://linkedin.com/in/nobhokleng" target="_blank" rel="noopener" class="group social-link-enhanced" aria-label="LinkedIn Profile">
                <div class="relative w-14 h-14 bg-white/70 dark:bg-secondary-800/70 backdrop-blur-sm border border-white/20 dark:border-secondary-700/50 rounded-2xl transition-all duration-500 group-hover:scale-110 group-hover:-translate-y-2 group-hover:shadow-xl group-hover:shadow-primary-500/25 flex items-center justify-center overflow-hidden">
                  <!-- Hover Gradient Overlay -->
                  <div class="absolute inset-0 bg-gradient-to-br from-primary-500/0 to-accent-500/0 group-hover:from-primary-500/10 group-hover:to-accent-500/10 transition-all duration-500 rounded-2xl"></div>
                  <svg class="relative z-10 w-6 h-6 text-secondary-600 dark:text-secondary-300 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-all duration-300" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                  </svg>
                </div>
              </a>
              
              <a href="https://github.com/Nobhokleng" target="_blank" rel="noopener" class="group social-link-enhanced" aria-label="GitHub Profile">
                <div class="relative w-14 h-14 bg-white/70 dark:bg-secondary-800/70 backdrop-blur-sm border border-white/20 dark:border-secondary-700/50 rounded-2xl transition-all duration-500 group-hover:scale-110 group-hover:-translate-y-2 group-hover:shadow-xl group-hover:shadow-primary-500/25 flex items-center justify-center overflow-hidden">
                  <div class="absolute inset-0 bg-gradient-to-br from-primary-500/0 to-accent-500/0 group-hover:from-primary-500/10 group-hover:to-accent-500/10 transition-all duration-500 rounded-2xl"></div>
                  <svg class="relative z-10 w-6 h-6 text-secondary-600 dark:text-secondary-300 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-all duration-300" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                  </svg>
                </div>
              </a>
              
              <a href="mailto:<EMAIL>" class="group social-link-enhanced" aria-label="Email Contact">
                <div class="relative w-14 h-14 bg-white/70 dark:bg-secondary-800/70 backdrop-blur-sm border border-white/20 dark:border-secondary-700/50 rounded-2xl transition-all duration-500 group-hover:scale-110 group-hover:-translate-y-2 group-hover:shadow-xl group-hover:shadow-primary-500/25 flex items-center justify-center overflow-hidden">
                  <div class="absolute inset-0 bg-gradient-to-br from-primary-500/0 to-accent-500/0 group-hover:from-primary-500/10 group-hover:to-accent-500/10 transition-all duration-500 rounded-2xl"></div>
                  <svg class="relative z-10 w-6 h-6 text-secondary-600 dark:text-secondary-300 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-all duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                  </svg>
                </div>
              </a>
              
              <!-- Call-to-Action Contact Button -->
              <a href="/contact" class="group">
                <div class="relative w-auto h-14 px-6 bg-gradient-to-r from-primary-600 to-accent-600 rounded-2xl transition-all duration-500 group-hover:scale-105 group-hover:-translate-y-2 group-hover:shadow-xl group-hover:shadow-primary-500/30 flex items-center justify-center overflow-hidden">
                  <div class="absolute inset-0 bg-gradient-to-r from-white/0 to-white/0 group-hover:from-white/10 group-hover:to-white/20 transition-all duration-500"></div>
                  <span class="relative z-10 text-white font-semibold text-sm flex items-center gap-2">
                    <span>Get in Touch</span>
                    <svg class="w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                    </svg>
                  </span>
                </div>
              </a>
            </div>
          </div>
        </div>

        <!-- Enhanced Navigation Section -->
        <div class="lg:col-span-5">
          <div class="lg:pl-8">
            <h4 class="text-sm font-bold text-secondary-700 dark:text-secondary-300 mb-6 uppercase tracking-widest flex items-center gap-2">
              <span class="w-2 h-2 bg-gradient-to-r from-primary-500 to-accent-500 rounded-full"></span>
              Site Navigation
            </h4>
            
            <!-- Enhanced Navigation Grid -->
            <nav aria-label="Footer navigation" class="space-y-2">
              <a href="/#about" class="group flex items-center justify-between p-3 rounded-xl hover:bg-white/50 dark:hover:bg-secondary-800/50 transition-all duration-300" aria-label="Go to About section">
                <span class="text-secondary-600 dark:text-secondary-300 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors duration-300">About</span>
                <svg class="w-4 h-4 text-secondary-400 group-hover:text-primary-500 group-hover:translate-x-1 transition-all duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
              </a>
              
              <a href="/#portfolio" class="group flex items-center justify-between p-3 rounded-xl hover:bg-white/50 dark:hover:bg-secondary-800/50 transition-all duration-300" aria-label="Go to Portfolio section">
                <span class="text-secondary-600 dark:text-secondary-300 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors duration-300">Portfolio</span>
                <svg class="w-4 h-4 text-secondary-400 group-hover:text-primary-500 group-hover:translate-x-1 transition-all duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
              </a>
              
              <a href="/contact" class="group flex items-center justify-between p-3 rounded-xl hover:bg-white/50 dark:hover:bg-secondary-800/50 transition-all duration-300" aria-label="Go to Contact page">
                <span class="text-secondary-600 dark:text-secondary-300 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors duration-300">Contact</span>
                <svg class="w-4 h-4 text-secondary-400 group-hover:text-primary-500 group-hover:translate-x-1 transition-all duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
              </a>
              
              <a href="/resume" class="group flex items-center justify-between p-3 rounded-xl hover:bg-white/50 dark:hover:bg-secondary-800/50 transition-all duration-300" aria-label="Go to Resume page">
                <span class="text-secondary-600 dark:text-secondary-300 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors duration-300">Resume</span>
                <svg class="w-4 h-4 text-secondary-400 group-hover:text-primary-500 group-hover:translate-x-1 transition-all duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
              </a>
              
              <a href="/resources" class="group flex items-center justify-between p-3 rounded-xl hover:bg-white/50 dark:hover:bg-secondary-800/50 transition-all duration-300" aria-label="Go to Resources page">
                <span class="text-secondary-600 dark:text-secondary-300 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors duration-300">Resources</span>
                <svg class="w-4 h-4 text-secondary-400 group-hover:text-primary-500 group-hover:translate-x-1 transition-all duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
              </a>
              
              <a href="/rss.xml" class="group flex items-center justify-between p-3 rounded-xl hover:bg-white/50 dark:hover:bg-secondary-800/50 transition-all duration-300" aria-label="Subscribe to RSS feed">
                <span class="text-secondary-600 dark:text-secondary-300 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors duration-300">RSS Feed</span>
                <svg class="w-4 h-4 text-secondary-400 group-hover:text-primary-500 group-hover:translate-x-1 transition-all duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
              </a>
            </nav>
          </div>
        </div>
      </div>

      <!-- Modern Bottom Section with Glass Separator -->
      <div class="relative">
        <!-- Glass Separator Line -->
        <div class="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-primary-500/30 to-transparent"></div>
        
        <div class="pt-8 flex flex-col lg:flex-row justify-between items-center gap-6">
          <!-- Enhanced Copyright with Micro-animation -->
          <div class="flex items-center gap-4">
            <div class="w-8 h-8 bg-gradient-to-br from-primary-500 to-accent-500 rounded-xl flex items-center justify-center">
              <span class="text-white font-bold text-sm">N</span>
            </div>
            <div class="text-sm text-secondary-600 dark:text-secondary-400">
              <span>&copy; {currentYear} Nob Hokleng. All rights reserved.</span>
            </div>
          </div>
          
          <!-- Tech Stack with Enhanced Visual Treatment -->
          <div class="flex items-center gap-6 text-sm">
            <div class="flex items-center gap-2 text-secondary-500 dark:text-secondary-400">
              <span class="w-2 h-2 bg-gradient-to-r from-primary-500 to-accent-500 rounded-full animate-pulse"></span>
              <span>Built with</span>
            </div>
            <div class="flex items-center gap-4">
              <span class="px-3 py-1 bg-white/50 dark:bg-secondary-800/50 backdrop-blur-sm rounded-lg text-secondary-600 dark:text-secondary-300 font-medium">Astro.js</span>
              <span class="px-3 py-1 bg-white/50 dark:bg-secondary-800/50 backdrop-blur-sm rounded-lg text-secondary-600 dark:text-secondary-300 font-medium">Tailwind CSS</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</footer> 