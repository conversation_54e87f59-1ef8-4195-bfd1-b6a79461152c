---
const currentYear = new Date().getFullYear();
---

<!-- Modern Compact Footer 2025 -->
<footer role="contentinfo" class="relative border-t border-secondary-300/60 dark:border-secondary-600/60">
  <!-- Subtle Background -->
  <div class="absolute inset-0 bg-gradient-to-br from-background-light/98 to-background-light-secondary/98 dark:from-background-dark/98 dark:to-background-dark-secondary/98"></div>

  <!-- Main Footer Content -->
  <div class="relative z-10">
    <div class="container-custom py-12">
      <!-- Compact Main Content Grid -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">

        <!-- Brand & Connect Section -->
        <div class="space-y-4">
          <div>
            <h3 class="text-xl font-bold font-heading bg-gradient-to-r from-primary-700 via-primary-600 to-accent-600 bg-clip-text text-transparent mb-2">
              Nob Hokleng
            </h3>
            <p class="text-sm text-secondary-600 dark:text-secondary-300 leading-relaxed">
              Software developer passionate about building scalable systems and modern web applications.
            </p>
          </div>

          <!-- Compact Social Links -->
          <div class="flex items-center gap-3">
            <a href="https://linkedin.com/in/nobhokleng" target="_blank" rel="noopener" class="group" aria-label="LinkedIn Profile">
              <div class="w-9 h-9 bg-white/60 dark:bg-secondary-800/60 border border-secondary-200/50 dark:border-secondary-700/50 rounded-lg transition-all duration-300 group-hover:scale-110 group-hover:bg-primary-500 group-hover:border-primary-500 flex items-center justify-center">
                <svg class="w-4 h-4 text-secondary-600 dark:text-secondary-300 group-hover:text-white transition-colors duration-300" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                </svg>
              </div>
            </a>

            <a href="https://github.com/Nobhokleng" target="_blank" rel="noopener" class="group" aria-label="GitHub Profile">
              <div class="w-9 h-9 bg-white/60 dark:bg-secondary-800/60 border border-secondary-200/50 dark:border-secondary-700/50 rounded-lg transition-all duration-300 group-hover:scale-110 group-hover:bg-primary-500 group-hover:border-primary-500 flex items-center justify-center">
                <svg class="w-4 h-4 text-secondary-600 dark:text-secondary-300 group-hover:text-white transition-colors duration-300" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.30.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                </svg>
              </div>
            </a>

            <a href="mailto:<EMAIL>" class="group" aria-label="Email Contact">
              <div class="w-9 h-9 bg-white/60 dark:bg-secondary-800/60 border border-secondary-200/50 dark:border-secondary-700/50 rounded-lg transition-all duration-300 group-hover:scale-110 group-hover:bg-primary-500 group-hover:border-primary-500 flex items-center justify-center">
                <svg class="w-4 h-4 text-secondary-600 dark:text-secondary-300 group-hover:text-white transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
              </div>
            </a>

            <!-- Compact CTA -->
            <a href="/contact" class="group ml-2">
              <div class="px-4 py-2 bg-gradient-to-r from-primary-600 to-accent-600 rounded-lg transition-all duration-300 group-hover:scale-105 group-hover:shadow-lg group-hover:shadow-primary-500/25">
                <span class="text-white font-medium text-sm">Get in Touch</span>
              </div>
            </a>
          </div>
        </div>

        <!-- Portfolio Navigation -->
        <div>
          <h4 class="text-sm font-semibold text-secondary-700 dark:text-secondary-300 mb-3">
            Explore
          </h4>
          <nav aria-label="Footer navigation" class="grid grid-cols-2 gap-2">
            <a href="/#about" class="text-sm text-secondary-600 dark:text-secondary-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-300" aria-label="Go to About section">About</a>
            <a href="/#portfolio" class="text-sm text-secondary-600 dark:text-secondary-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-300" aria-label="Go to Portfolio section">Portfolio</a>
            <a href="/contact" class="text-sm text-secondary-600 dark:text-secondary-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-300" aria-label="Go to Contact page">Contact</a>
            <a href="/resume" class="text-sm text-secondary-600 dark:text-secondary-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-300" aria-label="Go to Resume page">Resume</a>
            <a href="/resources" class="text-sm text-secondary-600 dark:text-secondary-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-300" aria-label="Go to Resources page">Resources</a>
            <a href="/rss.xml" class="text-sm text-secondary-600 dark:text-secondary-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-300" aria-label="Subscribe to RSS feed">RSS Feed</a>
          </nav>
        </div>
      </div>

      <!-- Modern Bottom Section -->
      <div class="border-t border-secondary-300/60 dark:border-secondary-600/60 pt-4 mt-6">
        <div class="flex flex-col sm:flex-row justify-between items-center gap-4">
          <!-- Left: Copyright -->
          <div class="flex items-center gap-3">
            <div class="w-6 h-6 bg-gradient-to-br from-primary-500 to-accent-500 rounded-lg flex items-center justify-center">
              <span class="text-white font-bold text-xs">N</span>
            </div>
            <span class="text-xs text-secondary-600 dark:text-secondary-400">
              &copy; {currentYear} Nob Hokleng. All rights reserved.
            </span>
          </div>

          <!-- Right: Back to Top -->
          <button
            onclick="window.scrollTo({top: 0, behavior: 'smooth'})"
            class="group flex items-center gap-2 text-xs text-secondary-500 dark:text-secondary-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-300"
            aria-label="Back to top"
          >
            <span>Back to top</span>
            <svg class="w-3 h-3 group-hover:-translate-y-0.5 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</footer>